 import { Component, Input, Output, EventEmitter, OnDestroy, OnInit, OnChanges, SimpleChanges, inject, ChangeDetectorRef } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { environment } from '../../../../environments/environment';
import { KeycloakService } from '../../../services/keycloak.service';

export interface DropdownOption {
  [key: string]: any;
}

export interface DropdownConfig {
  type: 'type' | 'foreignKey' | 'regular' | 'id' | 'lookup';
  apiEndpoint?: string;
  queryBuilderId?: string;
  searchEnabled?: boolean;
  placeholder?: string;
  emptyMessage?: string;
  tooltip?: string;
  maxHeight?: string;
  limit?: number;
}

export interface DropdownValueChangeEvent {
  fieldName: string;
  value: any;
  option: DropdownOption;
  displayText: string;
}

@Component({
  selector: 'app-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './dropdown.component.html',
  styleUrl: './dropdown.component.scss'
})
export class DropdownComponent implements OnInit, OnDestroy, OnChanges {
  // Core inputs
  @Input() fieldName!: string;
  @Input() formControl!: FormControl;
  @Input() config!: DropdownConfig;
  @Input() isDisabled: boolean = false;
  @Input() isReadonly: boolean = false;
  @Input() options: DropdownOption[] = [];
  @Input() selectedValue: any = '';
  @Input() cssClass: string = '';
  @Input() inputId?: string;

  // Advanced configuration
  @Input() preloadedData: { [key: string]: DropdownOption[] } = {};
  @Input() fields: any[] = []; // For extracting original field names
  @Input() searchDebounceTime: number = 300;
  @Input() showArrowButton: boolean = true;
  @Input() autoClose: boolean = true;

  // Outputs
  @Output() valueChange = new EventEmitter<DropdownValueChangeEvent>();
  @Output() searchChange = new EventEmitter<string>();
  @Output() dropdownToggle = new EventEmitter<boolean>();
  @Output() optionSelect = new EventEmitter<DropdownOption>();

  // Internal state
  showDropdown: boolean = false;
  filteredOptions: DropdownOption[] = [];
  isLoading: boolean = false;
  searchTimeout: any;
  
  // Performance optimization: API response cache
  private apiCache: { [key: string]: DropdownOption[] } = {};

  // Lazy loading state management
  private hasLoadedData = false;
  private isFirstLoad = true;

  // Track when we're setting dropdown values to prevent input conflicts
  private settingDropdownValue: boolean = false;

  // Unique identifier for this dropdown instance
  public uniqueId: string = '';

  private http = inject(HttpClient);
  private keycloakService = inject(KeycloakService);
  private cdr = inject(ChangeDetectorRef);

  private getAuthHeaders() {
    const token = this.keycloakService.getToken();
    return {
      headers: new HttpHeaders({
        Authorization: `Bearer ${token}`
      })
    };
  }

  ngOnInit() {
    // Generate unique identifier for this dropdown instance
    this.uniqueId = this.inputId || `${this.fieldName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Initialize with preloaded data if available
    if (this.preloadedData && Object.keys(this.preloadedData).length > 0) {
      this.apiCache = { ...this.preloadedData };
      this.hasLoadedData = true;
      this.isFirstLoad = false;
    }

    // LAZY LOADING: Remove automatic preloading for performance
    // Data will be loaded when dropdown is first opened

    // Set initial filtered options if options are provided (for lookup types)
    if (this.options && this.options.length > 0) {
      this.filteredOptions = [...this.options];
      this.hasLoadedData = true;
      this.isFirstLoad = false;

      // Update cache for lookup type
      if (this.config.type === 'lookup') {
        const cacheKey = this.getCacheKey();
        this.apiCache[cacheKey] = this.options;
      }
    }

    // Update disabled state
    this.updateFormControlDisabledState();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Update form control disabled state when inputs change
    if (changes['isDisabled'] || changes['isReadonly']) {
      this.updateFormControlDisabledState();
    }

    // Update filtered options when options input changes
    if (changes['options'] && this.options) {
      this.filteredOptions = [...this.options];
      
      // Update cache for lookup type
      if (this.config.type === 'lookup') {
        const cacheKey = this.getCacheKey();
        this.apiCache[cacheKey] = this.options;
      }
    }

    // Update cache when preloaded data changes
    if (changes['preloadedData'] && this.preloadedData) {
      this.apiCache = { ...this.preloadedData };
    }
  }

  ngOnDestroy() {
    // Clear search timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  }

  private updateFormControlDisabledState(): void {
    if (this.formControl) {
      if (this.isDisabled || this.isReadonly) {
        if (this.formControl.enabled) {
          this.formControl.disable();
        }
      } else {
        if (this.formControl.disabled) {
          this.formControl.enable();
        }
      }
    }
  }

  toggleDropdown(): void {
    // Prevent interaction when disabled/readonly
    if (this.isDisabled || this.isReadonly) {
      return;
    }

    if (!this.showDropdown) {
      // LAZY LOADING: Load data on first open
      if (!this.hasLoadedData) {
        this.loadDataForFirstTime();
      } else {
        const currentValue = this.formControl?.value || '';
        if (currentValue.trim() === '') {
          this.loadAllOptions();
        } else {
          this.searchOptions(currentValue);
        }
      }
    } else {
      this.showDropdown = false;
    }

    this.dropdownToggle.emit(this.showDropdown);
  }

  onInputChange(event: Event): void {
    if (this.settingDropdownValue) {
      return; // Prevent conflicts when setting dropdown values
    }

    const target = event.target as HTMLInputElement;
    const searchTerm = target.value;

    // Clear existing timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Debounce search
    this.searchTimeout = setTimeout(() => {
      this.searchOptions(searchTerm);
      this.searchChange.emit(searchTerm);
    }, this.searchDebounceTime);
  }

  onInputFocus(): void {
    // Auto-open dropdown on focus if not disabled
    if (!this.isDisabled && !this.isReadonly && !this.showDropdown) {
      // LAZY LOADING: Load data on first focus if not loaded
      if (!this.hasLoadedData) {
        this.loadDataForFirstTime();
      } else {
        this.toggleDropdown();
      }
    }
  }

  /**
   * LAZY LOADING: Load dropdown data for the first time
   */
  private loadDataForFirstTime(): void {
    // Handle lookup type - use preloaded options
    if (this.config.type === 'lookup' && this.options && this.options.length > 0) {
      this.filteredOptions = [...this.options];
      this.hasLoadedData = true;
      this.isFirstLoad = false;
      this.showDropdown = true;
      this.dropdownToggle.emit(true);
      return;
    }

    // For API-based dropdowns, load from API
    const cacheKey = this.getCacheKey();

    // Check if already cached from preloaded data
    if (this.apiCache[cacheKey]) {
      this.filteredOptions = this.apiCache[cacheKey];
      this.hasLoadedData = true;
      this.isFirstLoad = false;
      this.showDropdown = true;
      this.dropdownToggle.emit(true);
      return;
    }

    // Load from API for the first time
    this.loadFromApiFirstTime();
  }

  /**
   * LAZY LOADING: Load from API for the first time with proper state management
   */
  private loadFromApiFirstTime(): void {
    const apiUrl = this.getApiUrl();
    const payload = this.getApiPayload();

    if (!apiUrl) {
      this.setEmptyDropdownState();
      return;
    }

    this.isLoading = true;

    this.http.post<any[]>(apiUrl, payload, this.getAuthHeaders()).subscribe({
      next: (response: any) => {
        this.isLoading = false;

        // Handle the API response structure with data property
        let responseData: any[] = [];
        if (response && response.data && Array.isArray(response.data)) {
          responseData = response.data;
        } else if (Array.isArray(response)) {
          responseData = response;
        }

        if (responseData.length > 0) {
          // Cache the response for future use
          const cacheKey = this.getCacheKey();
          this.apiCache[cacheKey] = responseData;
          this.filteredOptions = responseData;
          this.hasLoadedData = true;
          this.isFirstLoad = false;
          this.showDropdown = true;
          this.dropdownToggle.emit(true);
        } else {
          this.setEmptyDropdownState();
        }
      },
      error: () => {
        this.isLoading = false;
        this.setEmptyDropdownState();
      }
    });
  }

  onInputBlur(): void {
    // Delay hiding dropdown to allow click on dropdown items
    if (this.autoClose) {
      setTimeout(() => {
        this.showDropdown = false;
        this.dropdownToggle.emit(false);
      }, 200);
    }
  }

  selectOption(option: DropdownOption): void {
    this.setDropdownValue(option);
    this.optionSelect.emit(option);
  }

  searchOptions(searchTerm: string): void {
    if (searchTerm.trim() === '') {
      this.loadAllOptions();
      return;
    }

    // Use server-side filtering for all dropdown types that support it
    if (this.config.type === 'id' || this.config.type === 'type' || this.config.type === 'foreignKey' || this.config.type === 'regular') {
      this.loadFromApi(searchTerm);
      return;
    }

    // For lookup dropdowns, use client-side filtering with preloaded options
    if (this.config.type === 'lookup') {
      this.loadAllAndFilter(searchTerm);
      return;
    }

    // Fallback to client-side filtering for any other types
    this.loadAllAndFilter(searchTerm);
  }

  loadAllOptions(): void {
    const cacheKey = this.getCacheKey();

    // Handle lookup type - use preloaded options
    if (this.config.type === 'lookup' && this.options && this.options.length > 0) {
      this.filteredOptions = [...this.options];
      this.showDropdown = true;
      this.hasLoadedData = true;
      return;
    }

    // Use cached data for superior performance
    if (this.apiCache[cacheKey]) {
      this.filteredOptions = this.apiCache[cacheKey];
      this.showDropdown = true;
      this.hasLoadedData = true;
      return;
    }

    // LAZY LOADING: If no cached data, load from API
    this.loadFromApi();
  }

  private getCacheKey(): string {
    if (this.config.queryBuilderId) {
      return this.config.queryBuilderId;
    }
    
    switch (this.config.type) {
      case 'type':
        return 'fieldType';
      case 'foreignKey':
        return 'formDefinition';
      case 'regular':
        // Extract foreign key from field configuration
        const originalFieldName = this.extractOriginalFieldName(this.fieldName);
        const field = this.fields.find(f => f.fieldName === originalFieldName);
        return field?.foreginKey || 'unknown';
      case 'id':
        return this.config.queryBuilderId || 'id';
      case 'lookup':
        return 'lookup';
      default:
        return 'default';
    }
  }

  private loadFromApi(searchTerm?: string): void {
    // Handle lookup type - use preloaded options
    if (this.config.type === 'lookup' && this.options && this.options.length > 0) {
      if (searchTerm && searchTerm.trim() !== '') {
        // Filter lookup options based on search term
        const filtered = this.options.filter(option => {
          return Object.values(option).some(value =>
            value && value.toString().toLowerCase().includes(searchTerm.toLowerCase())
          );
        });
        this.filteredOptions = filtered;
      } else {
        this.filteredOptions = [...this.options];
      }
      this.showDropdown = true;
      return;
    }

    const apiUrl = this.getApiUrl();
    const payload = this.getApiPayload(searchTerm);

    if (!apiUrl) {
      this.setEmptyDropdownState();
      return;
    }

    this.isLoading = true;

    this.http.post<any[]>(apiUrl, payload, this.getAuthHeaders()).subscribe({
      next: (response: any) => {
        this.isLoading = false;
        
        // Handle the API response structure with data property
        let responseData: any[] = [];
        if (response && response.data && Array.isArray(response.data)) {
          responseData = response.data;
        } else if (Array.isArray(response)) {
          responseData = response;
        }
        
        if (responseData.length > 0) {
          // Only cache if no search term (full data)
          if (!searchTerm || searchTerm.trim() === '') {
            const cacheKey = this.getCacheKey();
            this.apiCache[cacheKey] = responseData;
            this.hasLoadedData = true;
          }
          this.filteredOptions = responseData;
          this.showDropdown = true;
        } else {
          this.setEmptyDropdownState();
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.setEmptyDropdownState();
      }
    });
  }

  private getApiUrl(): string {
    if (this.config.apiEndpoint) {
      return this.config.apiEndpoint;
    }

    // For lookup type, return empty string as we use preloaded options
    if (this.config.type === 'lookup') {
      return '';
    }

    const queryBuilderId = this.getCacheKey();
    return `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
  }

  private getApiPayload(searchTerm?: string): any {
    const basePayload: any = {};

    // Set the correct _select field based on dropdown type
    if (this.config.type === 'id') {
      basePayload._select = ["ID"];
    } else if (this.config.type === 'type') {
      basePayload._select = ["ID", "Desc"];
    } else if (this.config.type === 'foreignKey') {
      basePayload._select = ["ID", "Desc"];
    } else if (this.config.type === 'regular') {
      basePayload._select = ["ID", "Desc"];
    } else {
      basePayload._select = ["ID"];
    }

    // Add server-side filtering for all dropdown types
    if (searchTerm && searchTerm.trim() !== '') {
      if (this.config.type === 'id') {
        basePayload.ID = {
          CT: searchTerm // CT = Contains operator
        };
      } else if (this.config.type === 'type' || this.config.type === 'foreignKey' || this.config.type === 'regular') {
        // For type, foreignKey, and regular dropdowns, search in both ID and Desc fields
        basePayload.ID = {
          CT: searchTerm
        };
        basePayload.Desc = {
          CT: searchTerm
        };
      }
    }

    // Add _limit for all dropdown types that support it
    if (this.config.type === 'id' || this.config.type === 'type' || this.config.type === 'foreignKey' || this.config.type === 'regular') {
      if (this.config.limit) {
        basePayload._limit = this.config.limit;
      } else {
        basePayload._limit = 20;
      }
    }

    return basePayload;
  }

  private loadAllAndFilter(searchTerm: string): void {
    const cacheKey = this.getCacheKey();

    // Handle lookup type - use preloaded options
    if (this.config.type === 'lookup' && this.options && this.options.length > 0) {
      const filtered = this.options.filter(option => {
        // For lookup fields, search in label only
        const label = option['label'] || '';
        return label.toLowerCase().includes(searchTerm.toLowerCase());
      });
      this.filteredOptions = filtered;
      this.showDropdown = true;
      return;
    }

    // Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      const filtered = this.apiCache[cacheKey].filter(option => {
        // For Regular dropdowns, search across all properties
        if (this.config.type === 'regular') {
          return Object.values(option).some(value =>
            value && value.toString().toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
        // For Type and Foreign Key dropdowns, filter by ID and Desc fields
        else {
          const idMatch = option['ID'] && option['ID'].toString().toLowerCase().includes(searchTerm.toLowerCase());
          const descMatch = option['Desc'] && option['Desc'].toString().toLowerCase().includes(searchTerm.toLowerCase());
          return idMatch || descMatch;
        }
      });
      this.filteredOptions = filtered;
      this.showDropdown = true;
      return;
    }

    // Fallback: Load all data from API and then filter client-side
    const apiUrl = this.getApiUrl();
    const payload = this.getApiPayload(); // No search term for client-side filtering

    if (!apiUrl) {
      this.setEmptyDropdownState();
      return;
    }

    this.isLoading = true;
    this.http.post<any[]>(apiUrl, payload, this.getAuthHeaders()).subscribe({
      next: (response: any) => {
        this.isLoading = false;
        
        // Handle the API response structure with data property
        let responseData: any[] = [];
        if (response && response.data && Array.isArray(response.data)) {
          responseData = response.data;
        } else if (Array.isArray(response)) {
          responseData = response;
        }
        
        if (responseData.length > 0) {
          // Cache the full response for future client-side filtering
          this.apiCache[cacheKey] = responseData;

          // Filter the response based on search term
          const filtered = responseData.filter(option => {
            // For Regular dropdowns, search across all properties
            if (this.config.type === 'regular') {
              return Object.values(option).some(value =>
                value && value.toString().toLowerCase().includes(searchTerm.toLowerCase())
              );
            }
            // For Type and Foreign Key dropdowns, filter by ID and Desc fields
            else {
              const idMatch = option['ID'] && option['ID'].toString().toLowerCase().includes(searchTerm.toLowerCase());
              const descMatch = option['Desc'] && option['Desc'].toString().toLowerCase().includes(searchTerm.toLowerCase());
              return idMatch || descMatch;
            }
          });

          this.filteredOptions = filtered;
          this.showDropdown = true;
        } else {
          this.setEmptyDropdownState();
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.setEmptyDropdownState();
      }
    });
  }

  private setEmptyDropdownState(): void {
    this.filteredOptions = [];
    this.showDropdown = true;
  }

  private setDropdownValue(option: DropdownOption): void {
    // Mark that we're setting a dropdown value to prevent input conflicts
    this.settingDropdownValue = true;

    if (this.formControl) {
      // Get the display text for the input field
      const displayText = this.getOptionDisplayText(option);

      // For all dropdown types, store the ID property since API now returns ID for all types
      const storedValue = option['ID'] || '';
      this.formControl.setValue(storedValue);

      // Set the input element's display value to the human-readable text
      setTimeout(() => {
        const inputElement = document.getElementById(this.uniqueId) as HTMLInputElement;
        if (inputElement) {
          inputElement.value = displayText;
        }
      }, 0);

      // Force change detection and validation
      this.formControl.markAsDirty();
      this.formControl.markAsTouched();
      this.formControl.updateValueAndValidity();

      // Force Angular change detection
      this.cdr.detectChanges();
    }

    // Close dropdown
    this.showDropdown = false;

    // Emit value change event with unique identifier
    const displayText = this.getOptionDisplayText(option);
    const storedValue = option['ID'] || '';
    this.valueChange.emit({
      fieldName: this.uniqueId, // Use unique identifier instead of fieldName
      value: storedValue,
      option: option,
      displayText: displayText
    });

    // Clear the dropdown value setting flag after a short delay
    setTimeout(() => {
      this.settingDropdownValue = false;
    }, 100);
  }

  /**
   * Get the display text for an option (human-readable text to show in input)
   */
  public getOptionDisplayText(option: DropdownOption): string {
    if (!option) return '';

    // For ID fields, return the ID property
    if (this.config.type === 'id') {
      return option['ID'] || '';
    }

    // For type fields, prioritize Desc field, then ID field
    if (this.config.type === 'type') {
      return option['Desc'] || option['ID'] || '';
    }

    // For lookup fields, return the label property
    if (this.config.type === 'lookup') {
      return option['label'] || '';
    }

    // For regular dropdowns, prioritize meaningful fields and avoid duplication
    if (this.config.type === 'regular') {
      // Try to find a meaningful display field
      const displayFields = ['name', 'title', 'description', 'label', 'text', 'value', 'Desc'];
      for (const field of displayFields) {
        if (option[field] && option[field].toString().trim() !== '') {
          return option[field].toString();
        }
      }
      
      // If no meaningful field found, get the first non-empty property
      const keys = Object.keys(option).filter(key => 
        option[key] && 
        option[key].toString().trim() !== ''
      );
      if (keys.length > 0) {
        return option[keys[0]].toString();
      }
    }

    // For other foreign key fields, prioritize Desc field, then ID field
    if (this.config.type === 'foreignKey') {
      return option['Desc'] || option['ID'] || '';
    }

    // For other foreign key fields, get the first property
    const keys = Object.keys(option);
    if (keys.length > 0) {
      return option[keys[0]] || '';
    }

    return '';
  }

  /**
   * Extract the original field name from complex field names like:
   * - fieldName_group_k
   * - fieldName_nested_k_n
   * - fieldName_group_k_multi_l
   * - fieldName_j (for multi-fields)
   */
  private extractOriginalFieldName(fieldName: string): string {
    if (fieldName.includes('_nested_')) {
      return fieldName.split('_nested_')[0];
    } else if (fieldName.includes('_group_')) {
      return fieldName.split('_group_')[0];
    } else if (fieldName.includes('_')) {
      // Handle simple multi-field pattern like fieldName_j
      const parts = fieldName.split('_');
      if (parts.length === 2 && !isNaN(parseInt(parts[1]))) {
        return parts[0];
      }
      return fieldName;
    }
    return fieldName;
  }

  // REMOVED: preloadDropdownData() method - replaced with lazy loading

  /**
   * Clear cache for this dropdown (useful for refreshing data)
   */
  public clearCache(): void {
    const cacheKey = this.getCacheKey();
    if (this.apiCache[cacheKey]) {
      delete this.apiCache[cacheKey];
    }
    this.hasLoadedData = false;
    this.isFirstLoad = true;
  }

  /**
   * Check if dropdown has cached data
   */
  public hasCachedData(): boolean {
    const cacheKey = this.getCacheKey();
    return !!this.apiCache[cacheKey];
  }

  // Utility methods for template
  getKeys(option: DropdownOption): string[] {
    return Object.keys(option);
  }

  // Performance optimization: trackBy functions
  trackByOptionId(_index: number, option: DropdownOption): string {
    // For ID dropdowns, use ID field; for type dropdowns, use ID field; for others, use ID
    if (this.config.type === 'id' || this.config.type === 'type') {
      return option['ID'] || '';
    }
    return option['ID'] || '';
  }

  trackByKey(_index: number, key: string): string {
    return key;
  }

  // Computed properties for template
  get inputClass(): string {
    // Start with base form-input class
    let classes = 'form-input';

    // Add any additional CSS classes passed in
    if (this.cssClass) {
      // If cssClass already contains 'form-input', don't duplicate it
      if (this.cssClass.includes('form-input')) {
        classes = this.cssClass;
      } else {
        classes += ` ${this.cssClass}`;
      }
    }

    // Add disabled state if needed
    if (this.isDisabled || this.isReadonly) {
      classes += ' disabled';
    }

    return classes;
  }

  get dropdownArrowIcon(): string {
    return this.showDropdown ? 'keyboard_arrow_up' : 'keyboard_arrow_down';
  }

  get emptyMessage(): string {
    return this.config.emptyMessage || 'No options found';
  }

  get placeholderText(): string {
    return this.config.placeholder || '';
  }

  get tooltipText(): string {
    return this.config.tooltip || 'Show options';
  }

  get dropdownMaxHeight(): string {
    return this.config.maxHeight || '200px';
  }
}
